                        -H/Users/<USER>/flutter/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-DANDROID_PLATFORM=android-23
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.2.12479018
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.2.12479018
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.2.12479018/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/NicheProjects/worde/build/app/intermediates/cxx/Debug/6p1s234c/obj/arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/NicheProjects/worde/build/app/intermediates/cxx/Debug/6p1s234c/obj/arm64-v8a
-DCMAKE_BUILD_TYPE=Debug
-B/Users/<USER>/Desktop/NicheProjects/worde/android/app/.cxx/Debug/6p1s234c/arm64-v8a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2