import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:mobx/mobx.dart';
import 'package:wordle/src/core/http/status.dart';
import 'package:wordle/src/core/utils/local_storage.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/domain/main/stores/main_store.dart';
import 'package:wordle/src/services/auth/auth_service.dart';
import 'package:wordle/src/ui/widgets/show_loading.dart';

import '../../../core/constants/firestore_keys.dart';
import '../../../core/constants/user_prop.dart';
import '../../../core/helpers/users.dart';
import '../../../data/repositories/ironsource_ad_repository.dart';
import '../../../router/start_up_controller.dart';
import '../../shop/store/shop_store.dart';

part 'auth_store.g.dart';

class AuthStore = _AuthStore with _$AuthStore;

abstract class _AuthStore with Store {
  final _authService = ServiceLocator.locate<AuthService>();
  final _localStorage = ServiceLocator.locate<LocalStorage>();
  final _mainStore = ServiceLocator.locate<MainStore>();
  final shopStore = ServiceLocator.locate<ShopStore>();
  final userMetaRef =
      FirebaseFirestore.instance.collection(FirestoreKeys.userMeta);
  final startup = StartUpController();
  final _functions = FirebaseFunctions.instance;

  @observable
  Status? loginStatus;
  @observable
  String errorMessage = '';
  @observable
  User? user;
  @observable
  bool isUserAnonymous = false;

  @action
  Future<void> loadAuth() async {
    await _authService.refreshUser().then((value) {
      user = _authService.user;
      isUserAnonymous = _authService.isUserAnonymous;
    });
  }

  @action
  void signInWithGoogle({
    bool linkGuest = false,
    required BuildContext context,
  }) {
    loginStatus = Status.pending;
    handleLogin(
      future: _authService.signInWithGoogle(linkGuest: linkGuest),
      context: context,
    );
  }

  @action
  void signInWithFacebook({
    bool linkGuest = false,
    required BuildContext context,
  }) {
    loginStatus = Status.pending;
    handleLogin(
      future: _authService.signInWithFacebook(linkGuest: linkGuest),
      context: context,
    );
  }

  @action
  void signInWithTwitter({
    bool linkGuest = false,
    required BuildContext context,
  }) {
    loginStatus = Status.pending;
    handleLogin(
      future: _authService.signInWithTwitter(linkGuest: linkGuest),
      context: context,
    );
  }

  @action
  void signInWithApple({
    bool linkGuest = false,
    required BuildContext context,
  }) {
    loginStatus = Status.pending;
    handleLogin(
      future: _authService.signInWithApple(
        linkGuest: linkGuest,
      ),
      context: context,
    );
  }

  @action
  void signOut() {
    loginStatus = Status.pending;
    _authService.signOut().then((value) {
      user = null;
      _localStorage.saveBool(LSKey.isFirstLaunchPlayPass, true);
      _mainStore.updatePlayPassValue(false);
      _mainStore.isSubscribed = false;
      loginStatus = Status.done;
    }).catchError((error) {
      if (error is FirebaseAuthException) {
        errorMessage = error.message ?? '';
      } else {
        errorMessage = 'An unexpected error occurred.';
      }
      loginStatus = Status.error;
    });
  }

  void handleLogin({
    required Future future,
    required BuildContext context,
  }) {
    future.then((value) async {
      user = _authService.user;
      _localStorage.saveBool(LSKey.hasSignedInOnSocial, true);
      await startup.initializeApp(shouldLogAppOpened: false);
      await checkPlayPassAndSubscription();
      await updateIAPNetworkCall();
      await transferStatsNetworkCall(context: context);
      loginStatus = Status.done;
    }).catchError((error) {
      if (error is FirebaseAuthException) {
        errorMessage = error.message ?? '';
      } else {
        errorMessage = 'An unexpected error occurred.';
      }
      loginStatus = Status.error;
    });
  }

  Future<void> checkPlayPassAndSubscription() async {
    shopStore.isPurchaseVerificationReq = false;
    final userMeta = userMetaRef.doc(_authService.user!.uid).snapshots();
    await shopStore.restorePurchases();
    userMeta.listen((snapshot) async {
      bool isSubscribed = await snapshot.data()?['isSubscribed'] ?? false;
      _mainStore.isSubscribed =
          IronSourceAdRepository.isSubscribed = isSubscribed;
      setUserProp(
        key: UserProp.isSubscribed,
        value: isSubscribed.toString(),
      );
    });
  }

  Future<void> updateIAPNetworkCall() async {
    final String? userId = user?.uid;
    String transactionId = shopStore.previousTransactionId;
    String? email = shopStore.previousSubscriptionEmail;
    bool? isPremiumUser = _mainStore.isPlayPass || _mainStore.isSubscribed;

    if (transactionId != "" &&
        userId != null &&
        email == null &&
        !isPremiumUser) {
      final Map<String, dynamic> data = {
        "userId": userId,
        "transactionId": transactionId,
      };

      final transferFunction =
          _functions.httpsCallable('updatePastPurchaseOfUser');

      try {
        final HttpsCallableResult<dynamic> response =
            await transferFunction(data);
        if (response.data) {
          debugPrint('Success updatePastPurchaseUser: $data\n${response.data}');
        } else {
          debugPrint('Error updatePastPurchaseUser: $data\n${response.data}');
        }
      } catch (error, stackTrace) {
        debugPrint(
            'Error updatePastPurchaseUser: $data\n${error.toString()} \n$stackTrace');
      } finally {
        _localStorage.remove(LSKey.oldUserId);
        _localStorage.remove(LSKey.shouldShowTransferIapPopup);
      }
    }
  }

  Future<void> transferStatsNetworkCall({
    required BuildContext context,
  }) async {
    final String? userId = user?.uid;
    String? oldUserId = _localStorage.retrieve(LSKey.oldUserId);

    if (oldUserId.isNotEmpty) {
      final Map<String, dynamic> data = {
        "fromAccount": oldUserId,
        "toAccount": userId,
      };

      final transferFunction =
          _functions.httpsCallable('transferDataFromGuestAccount');

      showLoading(context, "Transferring Stats...");
      try {
        final HttpsCallableResult<dynamic> response =
            await transferFunction(data);
        if (response.data != null) {
          debugPrint(
              'Success transferDataFromGuestAccount: $data\n${response.data}');
        } else {
          debugPrint(
              'Error transferDataFromGuestAccount: $data\n${response.data}');
        }
      } catch (error, stackTrace) {
        debugPrint(
            'Error transferDataFromGuestAccount: $data\n${error.toString()} \n$stackTrace');
      } finally {
        _localStorage.remove(LSKey.oldUserId);
        hideLoading(context);
      }
    }
  }

  @computed
  bool get isAuthenticated => user != null;
}
