import 'dart:developer';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/core/utils/word_list.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/router/routes.dart';
import 'package:wordle/src/ui/views/entrance/entrance_view.dart';

class FcmService {
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  static final FlutterLocalNotificationsPlugin
      _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
  static final wordList = ServiceLocator.locate<WordList>();
  final List<String> _channelIds = ['General', 'Reminders'];

  Future<void> initialise() async {
    await requestPermissionOnIos();
    await configureFCM();
    await configureLocalNotifications();
    await _createNotificationChannels();
  }

  getFirebaseMessaging() {
    return _firebaseMessaging;
  }

  Future<void> requestPermissionOnIos() async {
    if (defaultTargetPlatform == TargetPlatform.iOS) {
      await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );
    }
  }

  Future<void> configureLocalNotifications() async {
    //Local notifications - used to display locally on device
    const initializationSettingsAndroid =
        AndroidInitializationSettings('@drawable/ic_stat_name');
    const initializationSettingsIOS = DarwinInitializationSettings();
    const initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await _flutterLocalNotificationsPlugin.initialize(initializationSettings);
  }

  Future<void> configureFCM() async {
    // This is only for ios
    await _firebaseMessaging.setForegroundNotificationPresentationOptions(
      alert: true, // Required to display a heads up notification
      badge: true,
      sound: true,
    );

    FirebaseMessaging.onMessage.listen((remoteMessage) async {
      log('**onMessage** : ${remoteMessage.data}');
      await handleMessage(remoteMessage);
    });

    FirebaseMessaging.onMessageOpenedApp.listen((remoteMessage) async {
      log('**onMessage** : ${remoteMessage.data}');

      if (remoteMessage.data['app_route'] != null) {
        final validRoutes = routes.keys.toList();

        if (validRoutes.contains(remoteMessage.data['app_route'])) {
          AppRouter.push(remoteMessage.data['app_route']);
        } else {
          AppRouter.push(EntranceView.routeName);
        }
      } else {
        AppRouter.push(EntranceView.routeName);
      }
    });
  }

  Future<String?> getFcmToken() async {
    String? token;
    try {
      token = await _firebaseMessaging.getToken();
      debugPrint("FCM token: $token");
    } catch (e) {
      debugPrint('Error grabbing fcm token: ${e.toString()}');
    }
    return token;
  }

  static Future<void> handleMessage(RemoteMessage message) async {
    ///
    ///NOTE: Sending a push message with a notification payload while the app is in the background
    ///will automatically show the notification in the system tray. This behaviour should be handled by the
    ///sender and not the client.
    ///https://stackoverflow.com/questions/37966544/how-to-disable-showing-notification-when-it-it-comes-to-the-system-tray
    ///
    Map<String, dynamic> parsedMessage = {};

    //Extract notification data
    if (message.data.isNotEmpty) {
      parsedMessage = message.data;
      parsedMessage['title'] = message.notification!.title ?? '';
      parsedMessage['body'] = message.notification!.body ?? '';
    } else if (message.notification != null) {
      parsedMessage = {
        'title': message.notification!.title ?? '',
        'body': message.notification!.body ?? '',
      };
    }

    await displayLocalNotification(parsedMessage, messageId: message.hashCode);
  }

  static int get puzzleNumber {
    final past = DateTime(2021, 6, 19);
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    final seed = today.difference(past);
    return seed.inDays;
  }

  static Future<void> displayLocalNotification(Map<dynamic, dynamic> message,
      {int messageId = 0}) async {
    var vibrationPattern = Int64List(2);
    vibrationPattern[0] = 0;
    vibrationPattern[1] = 500;

    const notificationChannelId = 'General';
    var androidPlatformChannelSpecifics = AndroidNotificationDetails(
      notificationChannelId,
      notificationChannelId,
      channelDescription: notificationChannelId,
      playSound: true,
      enableVibration: true,
      vibrationPattern: vibrationPattern,
      importance: Importance.low,
      priority: Priority.low,
      visibility: NotificationVisibility.public,
      color: const Color(0xff6aaa64),
      colorized: true,
    );

    const iOSPlatformChannelSpecifics = DarwinNotificationDetails();

    var platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _flutterLocalNotificationsPlugin.show(
      messageId,
      message['title'] == 'Daily Challenge'
          ? "${message['title']} #$puzzleNumber"
          : message['title'],
      message['body'],
      platformChannelSpecifics,
    );
  }

  Future<void> _createNotificationChannels() async {
    for (var channelId in _channelIds) {
      await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(
              AndroidNotificationChannel(channelId, channelId));
    }
  }
}
