import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'dart:ui';

import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mobx/mobx.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:wordle/src/core/constants/app_const.dart';
import 'package:wordle/src/core/constants/events.dart';
import 'package:wordle/src/core/constants/font_family.dart';
import 'package:wordle/src/core/utils/service_locator.dart';
import 'package:wordle/src/data/models/difficulty_config.dart';
import 'package:wordle/src/data/models/stats.dart';
import 'package:wordle/src/services/events/event_logger_contract.dart';


import '../../core/constants/app_colors.dart';
import '../../core/constants/app_images.dart';
import '../../data/models/game_row.dart';

class StatsImageGenerator {
  static final _eventLogger = ServiceLocator.locate<EventLoggerContract>();
  static OverlayEntry? _mainOverlayEntry;
  static Future<void> shareStatsAsImage({
    required BuildContext context,
    required DifficultyConfig selectedDifficultyConfig,
    required ObservableList<GameRow> submittedRows,
    required Stats stats,
    required double batterThanPercentage,
  }) async {
    OverlayEntry overlayEntry = OverlayEntry(
      builder: (context) => ClipRRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            height: MediaQuery.of(context).size.height,
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.25),
            ),
            child: Stack(
              children: [
                Positioned(
                  child: Center(
                    child: _uiWidget(
                      context: context,
                      selectedDifficultyConfig: selectedDifficultyConfig,
                      submittedRows: submittedRows,
                      stats: stats,
                      batterThanPercentage: batterThanPercentage,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(overlayEntry);
    _mainOverlayEntry = overlayEntry;
  }

  static Widget _buildShareData({
    required GlobalKey renderKey,
    required Stats stats,
    required double batterThanPercentage,
  }) {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
      child: Center(
        child: RepaintBoundary(
          key: renderKey,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: Material(
              color: Colors.transparent,
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                padding: EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(30),
                  border: Border.all(
                    color: AppColors.greyB8,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SvgPicture.asset(
                          AppImages.logoGridThin,
                          height: 89,
                          width: 89,
                        ),
                        Text(
                          'Worde',
                          style: TextStyle(
                            fontSize: 28,
                            fontWeight: FontWeight.w900,
                            color: Colors.black,
                            fontFamily: FontFamily.stymie,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: _currentScoreWidget(
                        totalScore:
                            (stats.getSpeedGameDetailsModel?.totalPoints ?? 0)
                                .toString(),
                        linePoints:
                            (stats.getSpeedGameDetailsModel?.linePoints ?? 0)
                                .toString(),
                        gussesLength:
                            (stats.getSpeedGameDetailsModel?.gussesLength ?? 0)
                                .toString(),
                        secondsPoints:
                            (stats.getSpeedGameDetailsModel?.secondsPoints ?? 0)
                                .toString(),
                        usedSeconds:
                            (stats.getSpeedGameDetailsModel?.usedSeconds ?? 0)
                                .toString(),
                        batterThanPercentage: batterThanPercentage,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  static Future<void> shareStatsData(
      {required Stats stats,
      required double batterThanPercentage,
      required BuildContext context}) async {
    final GlobalKey renderKey = GlobalKey();
    Widget shareableWidget = _buildShareData(
        renderKey: renderKey,
        stats: stats,
        batterThanPercentage: batterThanPercentage);
    _eventLogger.log(Events.shareSpeedClicked);
    await Future.delayed(Duration(milliseconds: 50));
    OverlayEntry overlayEntry =
        OverlayEntry(builder: (context) => shareableWidget);
    Overlay.of(context).insert(overlayEntry);
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        final image = await _captureImage(renderKey);
        if (image == null) {
          debugPrint("Failed to capture image");
          return;
        }

        // Save the image as a file
        final tempDir = await getTemporaryDirectory();
        final file = await File('${tempDir.path}/wordle_stats.png').create();
        await file.writeAsBytes(image);

        // Share the file
        final xFile = XFile(file.path, mimeType: 'image/png');
        await SharePlus.instance.share(
          ShareParams(
            files: [xFile],
            text:
                'Wordle with a speedy twist!\nCan you beat my score?\n${AppConst.playStoreUrl}',
          ),
        );
      } catch (e) {
        debugPrint("Error sharing stats as image: $e");
      } finally {
        overlayEntry.remove();
        _mainOverlayEntry?.remove();
      }
    });
  }

  static Widget _uiWidget({
    required BuildContext context,
    required DifficultyConfig selectedDifficultyConfig,
    required ObservableList<GameRow> submittedRows,
    required Stats stats,
    required double batterThanPercentage,
  }) {
    return RepaintBoundary(
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Material(
          color: Colors.transparent,
          child: Stack(
            children: [
              Container(
                margin: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                padding: EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(30),
                  border: Border.all(
                    color: AppColors.greyB8,
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SvgPicture.asset(
                          AppImages.logoGridThin,
                          height: 89,
                          width: 89,
                        ),
                        Text(
                          'Worde',
                          style: TextStyle(
                            fontSize: 30,
                            fontWeight: FontWeight.w900,
                            color: Colors.black,
                            fontFamily: FontFamily.stymie,
                          ),
                        ),
                        SizedBox(height: 15),
                        Text(
                          'Wow! Great Score!',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                            fontFamily: FontFamily.urbanist,
                          ),
                        ),
                        SizedBox(height: 8),
                      ],
                    ),
                    SizedBox(width: 8),
                    Container(
                      child: _currentScoreWidget(
                          totalScore:
                              (stats.getSpeedGameDetailsModel?.totalPoints ?? 0)
                                  .toString(),
                          linePoints:
                              (stats.getSpeedGameDetailsModel?.linePoints ?? 0)
                                  .toString(),
                          gussesLength:
                              (stats.getSpeedGameDetailsModel?.gussesLength ?? 0)
                                  .toString(),
                          secondsPoints: (stats.getSpeedGameDetailsModel
                                      ?.secondsPoints ??
                                  0)
                              .toString(),
                          usedSeconds:
                              (stats.getSpeedGameDetailsModel?.usedSeconds ?? 0)
                                  .toString(),
                          batterThanPercentage: batterThanPercentage),
                    ),
                    SizedBox(
                      height: 15,
                    ),
                    SizedBox(
                      width: 124,
                      child: ElevatedButton.icon(
                        onPressed: () {
                          shareStatsData(
                              stats: stats,
                              batterThanPercentage: batterThanPercentage,
                              context: context);
                        },
                        icon: Icon(
                          Icons.share,
                          color: Colors.black,
                          size: 20,
                        ),
                        label: Text(
                          'SHARE',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.black,
                            fontFamily: FontFamily.urbanist,
                          )
                        ),
                        style: OutlinedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          side: BorderSide(
                            width: 1.0,
                            color: Colors.black,
                          ),
                          elevation: 0,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Positioned(
                right: 20,
                top: 15,
                child: IconButton(
                  alignment: Alignment.centerRight,
                  onPressed: () {
                    _mainOverlayEntry?.remove();
                  },
                  icon: Icon(
                    Icons.close,
                    color: Colors.black,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  static Future<Uint8List?> _captureImage(GlobalKey key) async {
    try {
      RenderRepaintBoundary? boundary =
          key.currentContext?.findRenderObject() as RenderRepaintBoundary?;

      if (boundary == null) {
        debugPrint("Error: Unable to find RenderRepaintBoundary");
        return null;
      }

      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);

      if (byteData == null) {
        debugPrint("Error: Unable to get byte data from image");
        return null;
      }

      return byteData.buffer.asUint8List();
    } catch (e) {
      debugPrint("Error capturing image: $e");
      return null;
    }
  }

  static Container _currentScoreWidget({
    required String totalScore,
    required String linePoints,
    required String gussesLength,
    required String secondsPoints,
    required String usedSeconds,
    required double batterThanPercentage,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: AppColors.darkYellow,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 10,
          ),
          yourPercentileBox(
              batterThanPercentage: batterThanPercentage.round().toString()),
          SizedBox(
            height: 4,
          ),
          // Text(
          //   "Score",
          //   style: TextStyle(
          //     color: AppColors.white,
          //     fontSize: 14,
          //     fontWeight: FontWeight.w700,
          //     fontFamily: FontFamily.urbanist,
          //   ),
          // ),
          Row(
            children: [
              Expanded(
                child: AutoSizeText(
                  totalScore,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 40,
                    fontWeight: FontWeight.w700,
                    fontFamily: FontFamily.urbanist,
                  ),
                ),
              ),
              SizedBox(
                width: 6,
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AutoSizeText(
                    "Solved in $gussesLength guesses",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      fontFamily: FontFamily.urbanist,
                    ),
                  ),
                  AutoSizeText(
                    "Solved in $usedSeconds sec",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.start,
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      fontFamily: FontFamily.urbanist,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  static Widget yourPercentileBox({required String batterThanPercentage}) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      decoration: BoxDecoration(
        border: Border.all(
          color: AppColors.white,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            AppImages.statsFilledIcon,
            color: Colors.white,
            height: 12,
          ),
          SizedBox(width: 8),
          Text(
            batterThanPercentage.toString() + 'th percentile',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.white,
              fontFamily: FontFamily.urbanist,
            ),
          ),
        ],
      ),
    );
  }
}
