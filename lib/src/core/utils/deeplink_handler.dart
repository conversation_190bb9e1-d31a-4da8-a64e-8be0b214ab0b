import 'dart:developer';

import 'package:app_links/app_links.dart';
import 'package:wordle/src/router/app_router.dart';
import 'package:wordle/src/router/routes.dart';

class DeepLinkHandler {
  static final _appLinks = AppLinks();

  static Future<void> setupDeepLinkHandler() async {
    // Handle app launch from a link
    final initialLink = await _appLinks.getInitialLink();
    if (initialLink != null) {
      handleDeepLink(initialLink.toString());
    }

    // Handle links while the app is running
    _appLinks.uriLinkStream.listen((uri) {
      handleDeepLink(uri.toString());
    });
  }
}

Future<void> handleDeepLink(String? link) async {
  const base = "https://worde.potudo.com";

  if (link != null) {
    final validRoutes = routes.keys.toList();
    final path = link.split(base).last;

    if (validRoutes.contains(path)) AppRouter.push(path);
  }

  log("Deeplink: $link");
}
